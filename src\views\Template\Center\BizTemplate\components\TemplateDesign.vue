<template>
  <div>
    <el-drawer
      size="80%"
      v-model="drawerShow"
      :before-close="handleClose"
      modal-class="tem-design-modal"
    >
      <template #header="{ titleClass }">
        <div v-loading="loading">
          <div class="header-box flex items-center justify-between" :class="titleClass">
            <h2 class="header-box-title single-ellipsis text-16px font-500 color-#72767b">
              {{ t('admin_template_temDesign') }} - {{ templateName }}
            </h2>
            <el-switch
              :model-value="isEdit"
              inline-prompt
              size="large"
              style="--el-switch-on-color: #2082ed; --el-switch-off-color: #909399"
              :active-text="t('admin_common_edit')"
              :inactive-text="t('admin_common_detail')"
              @change="isEditChange"
            />
          </div>
          <div class="mt-3px">
            <div class="flex items-center justify-between">
              <!-- 操作分组 -->
              <el-button-group>
                <el-button
                  v-for="(btnData, index) in dealBtn"
                  :key="index"
                  type="primary"
                  plain
                  @click="handleDeal(btnData.type)"
                >
                  <i class="iconfont mr-6px text-16px" :class="btnData.icon"></i>
                  {{ t(btnData.label) }}
                </el-button>
              </el-button-group>
              <!-- 发布 -->
              <div class="flex items-center text-14px font-400 color-#72767b">
                <span v-show="pageData?.publishedDate" class="flex items-center text-14px"
                  >{{ t('admin_template_lastReleaseTime') }}:
                  {{ getReleaseTime(pageData.publishedDate) }}</span
                >
                <el-button
                  type="primary"
                  class="release-btn ml-10px mr--23px"
                  plain
                  @click="handleRelease"
                  :disabled="!isEdit || !getEdited"
                >
                  <div class="tip" v-if="getEdited">{{ t('admin_common_edited') }}</div>
                  <i class="iconfont link-release mr-6px text-16px"></i>
                  {{ t('admin_template_release') }}
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </template>
      <div class="drawer-box h-100% w-100% flex justify-between bg-#f9f9f9 py-10px">
        <!-- aside -->
        <div class="aside h-100% w-[260px] bg-#fff px-10px py-10px" v-loading="loading">
          <div class="top-box flex items-center justify-between">
            <span class="text flex items-center text-16px">
              <div class="mr-8px h-16px w-4px rounded-6px bg-#2082ed"></div>
              {{ t('admin_template_processSettings') }}
            </span>
            <div>
              <el-button v-if="showResetSortBtn" size="small" @click="resetSort" type="primary">{{
                t('admin_common_reset')
              }}</el-button>
              <el-button
                v-if="showSaveSortBtn"
                size="small"
                @click="changeMsOrFormSort"
                type="primary"
                >{{ t('admin_common_save') }}</el-button
              >
            </div>
            <i
              class="iconfont link-zengjia text-18px color-#2082ed"
              @click="handleAsideDeal('ADD_MILESTONE', {})"
              v-if="isEdit"
            ></i>
          </div>
          <div class="mt-10px" :style="{ height: 'calc(100vh - 180px)', overflow: 'auto' }">
            <el-tree
              class="w-100%"
              ref="elTreeRef"
              :data="treeData"
              :draggable="true"
              :props="defaultProps"
              :highlight-current="true"
              :allow-drop="allowDrop"
              @node-drop="treeNodeDrop"
              :expand-on-click-node="false"
              :default-expand-all="true"
              :current-node-key="treeCurrentNodeKey"
              node-key="id"
              @node-click="handleNodeClick"
            >
              <template #default="{ node, data }">
                <div class="custom-tree-node w-100% flex items-center justify-between pr-8px">
                  <div class="flex items-center text-14px color-#595959">
                    <i
                      class="iconfont mr-5px color-#a1a5af"
                      :class="
                        data.formType == 'MAIN_FORM'
                          ? 'link-main-form'
                          : data.formType == 'MILESTONE'
                            ? 'link-folder'
                            : 'link-work-order'
                      "
                    ></i>
                    <div class="single-ellipsis w-140px">{{ node.label }}</div>
                  </div>
                  <div class="flex items-center">
                    <el-tooltip
                      class="box-item"
                      effect="dark"
                      :content="t('admin_template_createOrderTemTip')"
                      placement="right"
                    >
                      <i
                        v-if="data.formType == 'MAIN_FORM'"
                        class="iconfont link-question mr-6px text-16px color-#808080"
                      ></i>
                    </el-tooltip>
                    <div v-if="isEdit" class="flex items-center text-16px">
                      <i
                        v-show="data.formType == 'MILESTONE'"
                        class="iconfont link-zengjia mr-8px text-16px color-#808080"
                        @click.stop="handleAsideDeal('ADD_FORM', data)"
                      ></i>
                      <div @click.stop>
                        <el-popover
                          placement="bottom"
                          trigger="click"
                          popper-class="deal-milestone-popover"
                          :hide-after="0"
                        >
                          <div class="lk-table-btn-box flex-col pa-3px pl-0 pr-0">
                            <div
                              class="cursor-p mil-deal-btn px-5px py-5px"
                              @click="handleAsideDeal('Edit_' + data.formType, data)"
                            >
                              <i class="iconfont link-bianji mr-8px text-14px"></i>
                              <span>{{ t('admin_common_edit') }}</span>
                            </div>
                            <div
                              class="cursor-p mil-deal-btn px-5px py-5px"
                              v-if="data.formType != 'MAIN_FORM'"
                              @click="handleAsideDel(data.formType, data)"
                            >
                              <i class="iconfont link-delect mr-8px text-14px color-#E62412"></i>
                              <span class="color-#E62412">{{ t('admin_common_delect') }}</span>
                            </div>
                          </div>
                          <template #reference>
                            <i class="iconfont link-gengduo text-16px color-#808080"></i>
                          </template>
                        </el-popover>
                      </div>
                    </div>
                  </div>
                </div>
              </template>
            </el-tree>
          </div>
        </div>
        <!-- content -->
        <div
          class="content h-100%"
          :class="{
            'width-c-520': showType == 'MILESTONE',
            'width-c-260': showType != 'MILESTONE'
          }"
          v-loading="loading"
        >
          <div class="content-box h-100% w-100% bg-#fff">
            <div class="content-box-header" :class="{ 'border-bottom': showType != 'MILESTONE' }">
              <el-tabs
                v-if="showType === 'MILESTONE' && nodeData.needReply"
                v-model="milActiveName"
                class="demo-tabs"
                @tab-click="handleTabClick"
              >
                <el-tab-pane
                  v-if="nodeData.replyType !== 'REPLY_TO_WORK_ORDER'"
                  :label="t('admin_template_replyBtnName')"
                  name="REPLY_TO_MILESTONE"
                />
                <el-tab-pane
                  v-if="nodeData.replyType !== 'REPLY_TO_MILESTONE'"
                  :label="t('admin_template_taskReplyBtnName')"
                  name="REPLY_TO_WORK_ORDER"
                />
              </el-tabs>
              <el-button
                v-if="showType !== 'MILESTONE' || nodeData.needReply"
                type="primary"
                class="content-box-header-btn"
                plain
                @click="() => handleDesign()"
              >
                <i class="iconfont link-bianji mr-6px text-16px"></i>
                {{ getBtnNode() }}
              </el-button>
            </div>
            <div v-if="formCreateRenderData" class="content-box-body">
              <form-create
                :option="formCreateRenderData.optionsJson"
                :rule="formCreateRenderData.widgetJsonList"
              />
            </div>
          </div>
        </div>
        <!-- setting -->
        <div
          v-if="showType == 'MILESTONE'"
          class="setting h-100% w-[260px] bg-#fff"
          v-loading="loading"
        >
          <MilestonePropertiesForm
            ref="MilestonePropertiesFormRef"
            :data="nodeData"
            @save="onMilestonePropertiesFormSave"
            :readonly="!isEdit"
          />
        </div>
      </div>
    </el-drawer>
    <!-- 设计弹窗 -->
    <Dialog
      :title="getDialogTitle"
      v-model="designDialogVisible"
      size="huge"
      maxHeight="70vh"
      :beforeClose="handleDesignDialogClose"
    >
      <div class="fc-box" v-loading="fcDesignerLoading">
        <!-- 表单设计器 -->
        <FormDesigner
          v-if="designDialogVisible"
          ref="FormDesignerRef"
          :designerReadonly="designerReadonly"
        />
      </div>
      <template #footer>
        <div class="dialog-footer" v-if="designerReadonly">
          <el-button type="primary" @click="handleFormSave">
            <Icon class="mr-5px" icon="ep:plus" />
            {{ t('admin_common_save') }}
          </el-button>
        </div>
      </template>
    </Dialog>
    <!-- 配置 -->
    <Dialog :title="getConfigDialogTitle" v-model="configDialogVisible">
      <el-form
        ref="configFormRef"
        :model="configData"
        :rules="configRules"
        label-width="100px"
        v-loading="configLoading"
      >
        <el-form-item :label="getNameLabel" prop="name">
          <el-input
            v-model="configData.name"
            :rows="2"
            autosize
            show-word-limit
            maxlength="100"
            :placeholder="t('admin_common_inputText')"
          />
        </el-form-item>
        <el-form-item label="表单英文名称">
          <el-input
            v-model="configData.nameEn"
            :rows="2"
            autosize
            show-word-limit
            maxlength="100"
            :placeholder="t('admin_common_inputText')"
          />
        </el-form-item>
        <el-form-item
          :label="t('admin_common_description')"
          prop="desc"
          v-if="!['ADD_MILESTONE', 'Edit_MILESTONE'].includes(configType)"
        >
          <el-input
            v-model="configData.desc"
            type="textarea"
            :rows="2"
            autosize
            show-word-limit
            maxlength="200"
            :placeholder="t('admin_common_inputText')"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="configDialogVisible = false">{{ t('admin_common_cancel') }}</el-button>
        <el-button
          type="primary"
          color="#0070D2"
          :loading="configLoading"
          @click="handleConfigSure"
        >
          {{ t('admin_common_sure') }}
        </el-button>
      </template>
    </Dialog>
    <!-- PDF导入设置 -->
    <PdfImportDialog ref="pdfImportDialogRef" @handleClose="getData()" />
  </div>
</template>
<script setup lang="ts">
import FormDesigner from '@/components/FormDesigner/index.vue'
import { BizTemplateApi } from '@/api/template/bizTemplate'
import PdfImportDialog from './PDFImportDialog.vue'
import { ElMessageBox, FormInstance, FormRules } from 'element-plus'
import { ValidUtil } from '@/utils/validateUtils'
const message = useMessage()
import { formatTime } from '@/utils/index'
import MilestonePropertiesForm from './MilestonePropertiesForm.vue'
import { hasUpdateTemplate } from './injectionKey'
import { json } from 'stream/consumers'

const hasUpdate = ref<boolean>(false)

const setHasUpdate = (value: boolean) => {
  hasUpdate.value = value
}

provide(hasUpdateTemplate, { hasUpdate, setHasUpdate }) // 是否存在修改，用于阻止弹窗关闭和切换树节点

class FormVO {
  widgetJsonList?: any
  optionsJson?: any
  desc?: string
  name?: string
  id?: string
}

class configVO {
  name: string = ''
  desc?: string
}

const { t } = useI18n()

const pdfImportDialogRef = ref<any | HTMLElement>(null)
const isEdit = ref(false) // look edit
const drawerShow = ref(false)
const loading = ref(false)
const designDialogVisible = ref(false)
const designerReadonly = ref(false)
const fcDesignerLoading = ref(false)
const treeCurrentNodeKey = ref('')

const configType = ref('')
const configDialogVisible = ref(false)
const configLoading = ref(false)
const configData: any = reactive({})
const configRules = reactive<FormRules>({
  name: [ValidUtil.validRequiredForm()]
})
const configFormRef: Ref<FormInstance | undefined> = ref() // 表单 Ref

const templateName = ref('')
const bizSccsId = ref('')
const bizTemplateId = ref('')
const bizMongoId = ref('')
const nodeData = ref<any>({})
const showType = ref('MAIN_FORM') //MAIN_FORM MILESTONE FORM
const pageData = ref<any>({})
const treeData = ref<any>([])
const oldTemplateSortVo = ref<any>({})
const newTemplateSortVo = ref<any>({})
const elTreeRef = ref()
const templatePdfInfo = ref<any>({})

const defaultProps = {
  children: 'formList',
  label: 'name'
}

const dealBtn = reactive([
  // { label: 'admin_template_orderIdentification', icon: 'link-main-form', type: 'orderIdentification' },
  // { label: 'admin_template_businessRole', icon: 'link-shezhi', type: 'businessRole' },
  { label: 'admin_template_preview', icon: 'link-bianji', type: 'preview' },
  { label: 'admin_template_pdfImport', icon: 'link-pdf-import', type: 'pdfImport' }
])

const allowDrop = (
  draggingNode: { data: { formList: undefined; isMainForm: any } },
  dropNode: { data: { formList: undefined; isMainForm: any } },
  type: string
) => {
  var dragIsMs = draggingNode.data.formList != undefined
  var dropIsMs = dropNode.data.formList != undefined
  console.log('' + dragIsMs + dropIsMs + type)
  //避免用户操作时，刚好token过期，导致节点拖动出错
  // const result: Resp = await getCurrUserInfo();
  // if (result.code != 200) {
  //   return false;
  // }
  // if (!curTemplate.value.editable) {
  //   return false;
  // }
  // if (type == "prev") {
  //   return false;
  // }
  if (!isEdit.value) {
    return false
  }
  if (!dragIsMs && type == 'next' && dropIsMs) {
    return false
  }
  if (dragIsMs && !dropIsMs) {
    return false
  }
  if (draggingNode.data.isMainForm || dropNode.data.isMainForm) {
    return false
  }
  if (!dragIsMs && dropIsMs) {
    return type == 'inner'
  }
  if (!dragIsMs && !dropIsMs) {
    return draggingNode.data.milestoneId == dropNode.data.milestoneId&&type != 'inner'
  }
  if (dragIsMs && dropIsMs) {
    return type != 'inner'
  }
  return true
}
const showSaveSortBtn = ref(false)
const showResetSortBtn = ref(false)
const treeNodeDrop = (
  draggingNode: Node,
  dropNode: Node,
  dropType: NodeDropType,
  ev: DragEvents
) => {
  console.log(treeData.value)
  console.log(oldTemplateSortVo.value)
  newTemplateSortVo.value = getTemplateSortVoByTreeData(treeData.value)
  if (JSON.stringify(newTemplateSortVo.value) != JSON.stringify(oldTemplateSortVo.value)) {
    showSaveSortBtn.value = true
    showResetSortBtn.value = true
  } else {
    showSaveSortBtn.value = false
    showResetSortBtn.value = false
  }
}

const resetSort = () => {
  getData()
  showSaveSortBtn.value = false
  showResetSortBtn.value = false
  newTemplateSortVo.value = getTemplateSortVoByTreeData(treeData.value)
}

const changeMsOrFormSort = () => {
  BizTemplateApi.updateMsOrFormSort(newTemplateSortVo.value).then(() => {
    message.success(t('admin_common_updateSuccess'))
    showSaveSortBtn.value = false
    showResetSortBtn.value = false
    oldTemplateSortVo.value = getTemplateSortVoByTreeData(treeData.value)
    pageData.value.editTime = Date.now()
  })
}

const handleDeal = (type: string) => {
  if (type === 'pdfImport') {
    console.log(isEdit.value)
    const formId = treeData.value.find((treeItem) => treeItem.formType === 'MAIN_FORM').id
    pdfImportDialogRef.value.handleOpenDialog(
      formId,
      bizMongoId.value,
      templatePdfInfo.value,
      isEdit.value
    )
  }
}

const milActiveName = ref<'REPLY_TO_MILESTONE' | 'REPLY_TO_WORK_ORDER' | ''>('') //reply taskReply

const getReleaseTime = (time) => {
  return formatTime(time, 'yyyy-MM-dd HH:mm:ss')
}

const open = async (id, name, sccsId) => {
  drawerShow.value = true
  templateName.value = name
  bizTemplateId.value = id
  bizSccsId.value = sccsId
  await getData()
  if (pageData.value.mainForm) {
    handleNodeClick(pageData.value.mainForm)
    treeCurrentNodeKey.value = pageData.value.mainForm?.id
  }
}
const getData = async () => {
  try {
    loading.value = true
    // 获取订单里程碑
    const res = await BizTemplateApi.getBizTemplateDesign(bizTemplateId.value)
    res.mainForm.optionsJson = res.mainForm.optionsJson || {}
    res.mainForm.isMainForm = true
    treeData.value = []
    treeData.value = [res.mainForm, ...res.milestoneList]
    oldTemplateSortVo.value = getTemplateSortVoByTreeData(treeData.value)
    pageData.value = res
    bizMongoId.value = res.mongoId
    templatePdfInfo.value = res.templatePdfInfo
  } finally {
    loading.value = false
  }
}

//关闭回调
const handleClose = async (done: () => void) => {
  try {
    if (hasUpdate.value) {
      await ElMessageBox.confirm(`${nodeData.value.name}存在未保存修改`, '警告', {
        confirmButtonText: '丢弃',
        cancelButtonText: '返回保存',
        type: 'warning'
      })
    }
    isEdit.value = false
    nodeData.value = {}
    templateName.value = ''
    pageData.value = {}
    treeData.value = []
    done()
  } catch (error) {
    console.log('取消关闭')
  }
}

const isEditChange = async (value: boolean) => {
  try {
    if (value === false && hasUpdate.value) {
      await ElMessageBox.confirm(`${nodeData.value.name}存在未保存修改`, '警告', {
        confirmButtonText: '丢弃',
        cancelButtonText: '返回保存',
        type: 'warning'
      })
    }
    isEdit.value = value
  } catch (error) {
    console.log('取消关闭')
  }
}

const MilestonePropertiesFormRef = ref<any>(null)

//发布
const handleRelease = async () => {
  try {
    if (hasUpdate.value === true) {
      // 阻止切换树节点
      await ElMessageBox.confirm(`${nodeData.value.name}存在未保存修改`, '警告', {
        confirmButtonText: '丢弃',
        cancelButtonText: '返回保存',
        type: 'warning'
      })

      MilestonePropertiesFormRef.value?.resetForm()
    }

    await message.delConfirm(t('admin_template_releaseTip'), t('admin_template_releaseTitle'))
    loading.value = true

    await BizTemplateApi.publicTemplate({ id: pageData.value.mongoId })
    await getData()
    nextTick(() => {
      handleNodeClick(pageData.value.mainForm)
      elTreeRef.value.setCurrentKey(pageData.value.mainForm?.id)
    })
  } catch (error) {
    console.error(error)
  } finally {
    loading.value = false
  }
}

const handleNodeClick = async (data) => {
  try {
    if (nodeData.value.formType === 'MILESTONE' && hasUpdate.value === true) {
      // 阻止切换树节点
      await ElMessageBox.confirm(`${nodeData.value.name}存在未保存修改`, '警告', {
        confirmButtonText: '丢弃',
        cancelButtonText: '返回保存',
        type: 'warning'
      })
    }

    treeCurrentNodeKey.value = data.id
    nodeData.value = data
    showType.value = data.formType
    milActiveName.value = data.replyType === 'REPLY_TO_ALL' ? 'REPLY_TO_MILESTONE' : data.replyType
  } catch (error) {
    console.log('丢弃修改')
    elTreeRef.value.setCurrentKey(nodeData.value.id)
  }
}

const requestGetMilestoneDetail = async (id: string) => {
  try {
    const result = await BizTemplateApi.getMilestoneDetail(id)
    console.log(result)

    // nodeData.value = result
  } catch (error) {
    console.error(error)
  }
}

const requestGetFormDetail = async (id: string) => {
  try {
    const result = await BizTemplateApi.getFormDetail(id)
    console.log(result)
  } catch (error) {
    console.error(error)
  }
}

//node名称
const getBtnNode = () => {
  if (!isEdit.value) {
    return '查看'
  }
  const btnNode = {
    MAIN_FORM: t('admin_template_formDesign'), //主表单
    FORM: t('admin_template_formDesign') //表单
  }
  const milBtnNode = {
    REPLY_TO_MILESTONE: t('admin_template_replyBtnName'),
    REPLY_TO_WORK_ORDER: t('admin_template_taskReplyBtnName')
  }
  if (showType.value == 'MILESTONE') {
    return milBtnNode[milActiveName.value]
  } else {
    return btnNode[showType.value]
  }
}

const getDialogTitle = computed(() => {
  return getBtnNode() + '-' + nodeData.value?.name
})

//配置弹窗title
const titleList = {
  Edit_MILESTONE: t('admin_template_editMilestone'),
  Edit_MAIN_FORM: t('admin_template_editMain'),
  Edit_FORM: t('admin_template_editForm'),
  ADD_MILESTONE: t('admin_template_addMilestone'),
  ADD_FORM: t('admin_template_addForm')
}

const getConfigDialogTitle = computed(() => {
  return titleList[configType.value]
})

//菜单修改
const handleAsideDeal = (type: string, data: any) => {
  configType.value = type
  Object.assign(configData, new configVO())
  if (['Edit_MILESTONE', 'Edit_FORM', 'Edit_MAIN_FORM'].includes(type)) {
    configData['id'] = data.id
    configData['desc'] = data.desc
    configData['name'] = data.name
    configData['nameEn'] = data.nameEn
  } else if (type == 'ADD_FORM') {
    configData['milestoneId'] = data.id
  }
  configDialogVisible.value = true
}

const nameLabelList = {
  Edit_MILESTONE: t('admin_template_milestoneName'),
  Edit_MAIN_FORM: t('admin_template_mainName'),
  Edit_FORM: t('admin_template_formName'),
  ADD_MILESTONE: t('admin_template_milestoneName'),
  ADD_FORM: t('admin_template_formName')
}
const getNameLabel = computed(() => {
  return nameLabelList[configType.value]
})

//配置操作
const handleConfigSure = async () => {
  // 校验表单
  await configFormRef.value?.validate()
  // 提交请求
  configLoading.value = true
  try {
    switch (configType.value) {
      case 'Edit_MILESTONE':
        await BizTemplateApi.updateMilestone(configData)
        break
      case 'Edit_FORM':
      case 'Edit_MAIN_FORM':
        debugger
        await BizTemplateApi.updateForm(configData)
        break
      case 'ADD_MILESTONE':
        await BizTemplateApi.addMilestone({ templateId: pageData.value.mongoId, ...configData })
        break
      case 'ADD_FORM':
        await BizTemplateApi.addForm(configData)
        break
      default:
        break
    }
    message.success(t('admin_common_dealSuccess'))
    configDialogVisible.value = false
    await getData()
    nextTick(() => {
      nodeData.value = elTreeRef.value.getNode(nodeData.value.id).data
      elTreeRef.value.setCurrentKey(nodeData.value.id)
      milActiveName.value =
        nodeData.value.replyType === 'REPLY_TO_ALL'
          ? 'REPLY_TO_MILESTONE'
          : nodeData.value.replyType
    })
  } finally {
    configLoading.value = false
  }
}

//里程碑表表单删除
const handleAsideDel = async (type, { id }) => {
  try {
    if (type == 'MILESTONE') {
      // 二次确认
      await message.delConfirm(
        t('admin_template_delMilestoneTip'),
        t('admin_template_delMilestoneTitle')
      )
      loading.value = true
      await BizTemplateApi.deleteMilestone(id)
    } else if (type == 'FORM') {
      // 二次确认
      await message.delConfirm(t('admin_template_delFormTip'), t('admin_template_delFormTitle'))
      loading.value = true
      await BizTemplateApi.deleteForm(id)
    }
    message.success(t('admin_common_delSuccess'))
    await getData()
    nextTick(() => {
      handleNodeClick(pageData.value.mainForm)
      elTreeRef.value.setCurrentKey(pageData.value.mainForm?.id)
    })
  } finally {
    loading.value = false
  }
}

//是否已编辑
const getEdited = computed(() => {
  if (!pageData.value?.editTime) return false //刚创建未修改
  return pageData.value?.publishedDate < pageData.value?.editTime //已修改
})

const handleTabClick = () => {
  console.log('切换')
}

// 新增：处理设计弹窗关闭前检查
const handleDesignDialogClose = async (done: () => void) => {
  if (!FormDesignerRef.value) {
    done()
    return
  }

  // 检查表单数据是否有变更
  const hasChanged = FormDesignerRef.value.hasChanged()

  if (!hasChanged) {
    done()
    return
  }

  try {
    await ElMessageBox.confirm(t('admin_common_quit_tips'), t('admin_common_reminder'), {
      confirmButtonText: t('admin_common_discard'),
      cancelButtonText: t('admin_common_back'),
      type: 'warning'
    })

    if (FormDesignerRef.value) {
      FormDesignerRef.value.clearChangeStatus()
    }
    done()
  } catch {
    // 用户选择返回保存，不执行关闭操作
  }
}

const FormDesignerRef = ref<any>(null)
const handleDesign = () => {
  designDialogVisible.value = true
  designerReadonly.value = isEdit.value

  nextTick(() => {
    if (nodeData.value.formType === 'MILESTONE') {
      if (milActiveName.value === 'REPLY_TO_MILESTONE') {
        FormDesignerRef.value?.setRule(nodeData.value?.replyForm?.widgetJsonList)
      }
      if (milActiveName.value === 'REPLY_TO_WORK_ORDER') {
        FormDesignerRef.value?.setRule(nodeData.value?.workOrderReplyForm?.widgetJsonList)
      }
    } else {
      FormDesignerRef.value?.setRule(nodeData.value?.widgetJsonList)
    }

    console.log(nodeData.value)

    FormDesignerRef.value?.setInjectionData({
      currentFormId: nodeData.value.id,
      templateTreeData: treeData.value,
      formType: nodeData.value.formType,
      msFormType: milActiveName.value,
      formVisibleSettingList: nodeData.value.optionsJson?.form?.formVisibleSetting,
      sccsId: bizSccsId.value,
      msId: nodeData.value.milestoneId || nodeData.value.id,
      replyFormId:
        milActiveName.value === 'REPLY_TO_WORK_ORDER'
          ? nodeData.value.workOrderReplyFormId
          : nodeData.value.replyFormId,
      designerReadonly: isEdit.value
    })
  })
}

// 保存模板
const handleFormSave = async () => {
  if (!FormDesignerRef.value) return Promise.reject('FormDesigner 未初始化')
  fcDesignerLoading.value = true
  try {
    const data = {} as FormVO
    if (nodeData.value.formType === 'MILESTONE') {
      if (milActiveName.value === 'REPLY_TO_MILESTONE') {
        data.id = nodeData.value?.replyForm.id
        data.name = nodeData.value?.replyForm.name
        data.desc = nodeData.value?.replyForm.desc
      }

      if (milActiveName.value === 'REPLY_TO_WORK_ORDER') {
        data.id = nodeData.value?.workOrderReplyForm.id
        data.name = nodeData.value?.workOrderReplyForm.name
        data.desc = nodeData.value?.workOrderReplyForm.desc
      }
    } else {
      data.id = nodeData.value?.id
      data.name = nodeData.value?.name
      data.desc = nodeData.value?.desc
    }
    data.widgetJsonList = FormDesignerRef.value.getRule()
    data.optionsJson = FormDesignerRef.value.getOption()

    const tableFormList = data.widgetJsonList.filter((widget) => widget.type === 'DrTableForm')
    for (let i = 0, len = tableFormList.length; i < len; i++) {
      const tableFormItem = tableFormList[i]
      if (!tableFormItem.children || tableFormItem.children.length === 1) {
        message.error(`请完善${tableFormItem.title}配置`)
        return
      }
    }

    await BizTemplateApi.updateFormWithWidget(data)
    message.success(t('admin_common_updateSuccess'))

    // 清除表单设计器的修改状态
    if (FormDesignerRef.value) {
      FormDesignerRef.value.clearChangeStatus()
    }

    designDialogVisible.value = false
    await getData()
    nextTick(() => {
      nodeData.value = elTreeRef.value.getNode(nodeData.value.id).data
      elTreeRef.value.setCurrentKey(nodeData.value.id)
      milActiveName.value =
        nodeData.value.replyType === 'REPLY_TO_ALL'
          ? 'REPLY_TO_MILESTONE'
          : nodeData.value.replyType
    })
  } finally {
    fcDesignerLoading.value = false
  }
}

const formCreateRenderData = computed(() => {
  if (!nodeData.value) {
    return null
  }
  if (nodeData.value.formType === 'MILESTONE') {
    if (!nodeData.value.needReply) {
      return null
    }
    if (milActiveName.value === 'REPLY_TO_MILESTONE') {
      return {
        optionsJson: nodeData.value.replyForm.optionsJson || {},
        widgetJsonList: nodeData.value.replyForm.widgetJsonList || {}
      }
    }
    if (milActiveName.value === 'REPLY_TO_WORK_ORDER') {
      return {
        optionsJson: nodeData.value.workOrderReplyForm.optionsJson || {},
        widgetJsonList: nodeData.value.workOrderReplyForm.widgetJsonList || {}
      }
    }
  }

  return {
    optionsJson: nodeData.value.optionsJson,
    widgetJsonList: nodeData.value.widgetJsonList
  }
})

const onMilestonePropertiesFormSave = async ({ id }: { id: string }) => {
  console.log('onMilestonePropertiesFormSave', id)

  await getData()

  nextTick(() => {
    nodeData.value = elTreeRef.value.getNode(nodeData.value.id).data
    elTreeRef.value.setCurrentKey(nodeData.value.id)
    milActiveName.value =
      nodeData.value.replyType === 'REPLY_TO_ALL' ? 'REPLY_TO_MILESTONE' : nodeData.value.replyType
  })
}

defineExpose({ open })

function getTemplateSortVoByTreeData(treeData: any[]) {
  const templateSortVo = { templateId: String, msInfoList: [] }
  templateSortVo.templateId = treeData[0].templateId
  templateSortVo.msInfoList = []
  treeData.forEach((ms) => {
    if (!ms.formList) {
      return
    }
    const formIdList = ms.formList.map((form) => form.id)
    templateSortVo.msInfoList.push({
      msId: ms.id,
      formIdList
    })
  })
  return templateSortVo
}
</script>
<style scoped lang="scss">
.header-box {
  height: 50px;

  .header-box-title {
    max-width: 300px;
  }

  :deep(.el-switch--large) {
    .el-switch__core {
      padding: 6px;
    }
  }
}

.release-btn {
  position: relative;

  .tip {
    position: absolute;
    top: -10px;
    right: -25px;
    background: #e6a23c;
    border-radius: 10px;
    width: 40px;
    height: 18px;
    line-height: 18px;
    color: #fff;
    font-size: 12px;
    padding: 0 4px;
  }
}

//容器
.drawer-box {
  .aside {
    box-sizing: border-box;
  }

  .content {
    padding: 0 8px;
    box-sizing: border-box;

    &-box {
      &-header {
        position: relative;
        height: 40px;

        :deep(.el-tabs__header) {
          margin-bottom: 0 !important;
        }

        &-btn {
          position: absolute;
          top: 3px;
          right: 20px;
        }
      }

      .border-bottom {
        border-bottom: 2px solid #e8e8e8;
      }

      &-body {
        height: calc(100% - 40px);
        overflow: auto;
        padding: 10px 20px;
        box-sizing: border-box;
      }
    }
  }

  .width-c-260 {
    width: calc(100% - 260px);
  }

  .width-c-520 {
    width: calc(100% - 520px);
  }

  .setting {
    border-radius: 0;
  }
}

::v-deep(.content-box-body) {
  .dr-table-form {
    .dr-table-row {
      width: 100%;
      overflow-x: auto;
    }

    /* background: red; */
    .el-col-24 {
      display: table-cell;
      height: 200px;
      border-bottom: 1px solid #e4e7ed;

      &:first-child {
        display: none;
      }

      .dr-table-col {
        width: 180px !important;
        overflow: hidden;
        border-right: 1px solid #e4e7ed;

        .el-col-24 {
          display: block;

          .el-form-item__label {
            display: block;
            padding: 8px 0 8px 10px;
            border-bottom: 1px solid #e4e7ed;
          }

          .el-form-item__content {
            padding: 0 10px;
          }
        }
      }
    }
  }
}

:deep(.el-drawer__header) {
  margin-bottom: 0;
  align-items: flex-start;
  box-shadow: 0 1px 6px 0 rgba(0, 0, 0, 0.8);
  padding: 12px 20px;
  box-sizing: border-box;
}

:deep(.el-drawer__body) {
  padding: 0;
}

:deep(.el-drawer__close-btn) {
  margin-top: 12px;
}

.mil-deal-btn:hover {
  background: #f2f2f2;
}

:deep(.el-dialog__body) {
  padding: 0 !important;
}

.fc-box {
  height: 78vh;
}
</style>
<style>
.deal-milestone-popover {
  width: auto !important;
  min-width: 75px !important;
  padding: 3px 0 !important;
}
</style>
